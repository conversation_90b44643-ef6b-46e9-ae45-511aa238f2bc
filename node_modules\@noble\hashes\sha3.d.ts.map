{"version": 3, "file": "sha3.d.ts", "sourceRoot": "", "sources": ["src/sha3.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,IAAI,EAEJ,KAAK,EAIL,OAAO,EAGP,KAAK,EACL,OAAO,EACR,MAAM,YAAY,CAAC;AA0CpB,kFAAkF;AAClF,wBAAgB,OAAO,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM,GAAE,MAAW,GAAG,IAAI,CAyCjE;AAED,8BAA8B;AAC9B,qBAAa,MAAO,SAAQ,IAAI,CAAC,MAAM,CAAE,YAAW,OAAO,CAAC,MAAM,CAAC;IASxD,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,MAAM;IACd,SAAS,EAAE,MAAM;IACxB,SAAS,CAAC,SAAS;IACnB,SAAS,CAAC,MAAM,EAAE,MAAM;IAZ1B,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC;IAC5B,SAAS,CAAC,GAAG,SAAK;IAClB,SAAS,CAAC,MAAM,SAAK;IACrB,SAAS,CAAC,QAAQ,UAAS;IAC3B,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC;IAC/B,SAAS,CAAC,SAAS,UAAS;gBAGnB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,MAAM,EACd,SAAS,UAAQ,EACjB,MAAM,GAAE,MAAW;IAY/B,SAAS,CAAC,MAAM,IAAI,IAAI;IAOxB,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;IAYzB,SAAS,CAAC,MAAM,IAAI,IAAI;IAUxB,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAehD,OAAO,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAKpC,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAI9B,UAAU,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAOvC,MAAM,IAAI,UAAU;IAGpB,OAAO,IAAI,IAAI;IAIf,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM;CAehC;AAKD,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAA+C,CAAC;AACvE,yDAAyD;AACzD,eAAO,MAAM,QAAQ,EAAE,KAA+C,CAAC;AACvE,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAA+C,CAAC;AACvE,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAA8C,CAAC;AAEtE,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAA+C,CAAC;AACzE,yDAAyD;AACzD,eAAO,MAAM,UAAU,EAAE,KAA+C,CAAC;AACzE,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAA+C,CAAC;AACzE,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAA8C,CAAC;AAExE,MAAM,MAAM,SAAS,GAAG;IAAE,KAAK,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAQ3C,0CAA0C;AAC1C,eAAO,MAAM,QAAQ,EAAE,OAAsD,CAAC;AAC9E,0CAA0C;AAC1C,eAAO,MAAM,QAAQ,EAAE,OAAsD,CAAC"}