// =====================================================================================
// == CẢNH BÁO BẢO MẬT NGHIÊM TRỌNG / SERIOUS SECURITY WARNING
// =====================================================================================
// ==
// == VIỆC LƯU PRIVATE KEY (KHÓA BÍ MẬT) TRỰC TIẾP TRONG CODE NHƯ THẾ NÀY LÀ CỰC KỲ
// == NGUY HIỂM. BẤT KỲ AI CÓ QUYỀN TRUY CẬP VÀO FILE NÀY ĐỀU CÓ THỂ ĐÁNH CẮP
// == TOÀN BỘ TÀI SẢN TRONG VÍ CỦA BẠN.
// ==
// == BẠN ĐÃ YÊU CẦU "KHÔNG CẦN LO BẢO MẬT", VÌ VẬY TÔI GIỮ NGUYÊN CẤU TRÚC NÀY.
// == TUY NHIÊN, HÃY CHẮC CHẮN RẰNG BẠN HIỂU RÕ RỦI RO NÀY.
// ==
// == HARDCODING A PRIVATE KEY DIRECTLY IN THE SOURCE CODE IS EXTREMELY DANGEROUS.
// == ANYONE WITH ACCESS TO THIS FILE CAN STEAL ALL ASSETS FROM YOUR WALLET.
// ==
// =====================================================================================

// --- PHẦN 1: CÁC THƯ VIỆN CẦN THIẾT (IMPORTS) ---
const {
    Connection,
    Keypair,
    Transaction,
    sendAndConfirmTransaction,
    PublicKey,
    ComputeBudgetProgram,
} = require("@solana/web3.js");
const {
    createCloseAccountInstruction,
    createBurnCheckedInstruction,
} = require("@solana/spl-token");
const bs58 = require("bs58");

// --- PHẦN 2: CẤU HÌNH (CONFIGURATION) ---

// !!! THAY THẾ PRIVATE KEY CỦA BẠN VÀO ĐÂY !!!
// Private key phải là một chuỗi Base58 đầy đủ (thường dài 86-88 ký tự).
const PRIVATE_KEY = "YOUR_FULL_BASE58_PRIVATE_KEY_HERE";

// Địa chỉ RPC Node để kết nối đến Solana Blockchain.
// Bạn nên dùng RPC riêng để có hiệu suất tốt nhất.
const RPC_URL = "https://mainnet.helius-rpc.com/?api-key=942e4e75-d51a-438f-b45c-d6cee3dcc975";

// Danh sách các địa chỉ MINT của token bạn KHÔNG muốn đóng.
// Ví dụ: ["EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"] // USDC
const EXCLUDED_TOKEN_MINTS = [];

// Phí ưu tiên (tính bằng SOL) để giao dịch được xử lý nhanh hơn.
// Đặt là 0 nếu không muốn dùng.
const PRIORITY_FEE_SOL = 0.00001;

// Số lượng tài khoản tối đa để đóng trong một giao dịch.
// Solana có giới hạn về kích thước giao dịch, 10-15 là con số an toàn.
const BATCH_SIZE = 10;

// Thời gian chờ (mili giây) giữa mỗi lần gửi giao dịch hàng loạt.
// Giúp tránh bị RPC node chặn vì gửi quá nhiều yêu cầu.
const DELAY_BETWEEN_BATCHES_MS = 2000; // 2 giây

// --- PHẦN 3: KẾT NỐI VÀ CÁC HÀM TIỆN ÍCH ---

const connection = new Connection(RPC_URL, "confirmed");

/**
 * Hàm tiện ích để tạm dừng chương trình.
 * @param {number} ms - Thời gian chờ tính bằng mili giây.
 */
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Tạo các chỉ thị (instructions) để thiết lập phí ưu tiên cho giao dịch.
 * @param {number} priorityFeeInSol - Mức phí ưu tiên tính bằng SOL.
 * @returns {import("@solana/web3.js").TransactionInstruction[]} - Mảng các chỉ thị.
 */
function createPriorityFeeInstructions(priorityFeeInSol) {
    if (priorityFeeInSol <= 0) {
        return [];
    }
    // Ước tính số compute units cần thiết, có thể điều chỉnh nếu cần.
    const estimatedComputeUnits = 20000 * BATCH_SIZE;

    // 1 SOL = 1,000,000,000 Lamports
    // 1 Lamport = 1,000,000 MicroLamports
    const priorityFeeInMicroLamports = Math.floor((priorityFeeInSol * 1_000_000_000) / estimatedComputeUnits);

    const computeUnitLimitInstruction = ComputeBudgetProgram.setComputeUnitLimit({
        units: estimatedComputeUnits,
    });
    const computeUnitPriceInstruction = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: priorityFeeInMicroLamports,
    });

    return [computeUnitLimitInstruction, computeUnitPriceInstruction];
}


// --- PHẦN 4: LOGIC CHÍNH ĐỂ ĐÓNG TÀI KHOẢN ---

/**
 * Tìm, xử lý và đóng một lô (batch) các tài khoản token.
 * @param {import("@solana/web3.js").Keypair} signer - Keypair của ví cần dọn dẹp.
 * @returns {Promise<number>} - Số lượng tài khoản đã xử lý trong lô này.
 */
async function closeTokenAccountsBatch(signer) {
    console.log("\n🔍 Đang tìm kiếm các tài khoản token...");

    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(signer.publicKey, {
        programId: new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"),
    });

    console.log(`- Tìm thấy tổng cộng ${tokenAccounts.value.length} tài khoản token.`);

    const accountsToClose = tokenAccounts.value.filter(item => {
        const info = item.account.data.parsed.info;
        const isNativeSOL = info.mint === "So11111111111111111111111111111111111111112";
        const isExcluded = EXCLUDED_TOKEN_MINTS.includes(info.mint);
        const isFrozen = info.state === "frozen";
        
        // Chỉ đóng các tài khoản không phải là SOL, không nằm trong danh sách loại trừ,
        // và không bị đóng băng.
        return !isNativeSOL && !isExcluded && !isFrozen;
    });

    console.log(`- Có ${accountsToClose.length} tài khoản có thể đóng.`);

    if (accountsToClose.length === 0) {
        console.log("✅ Không còn tài khoản nào để đóng.");
        return 0; // Báo hiệu đã hoàn thành
    }
    
    // Chỉ xử lý một lô nhỏ mỗi lần
    const batch = accountsToClose.slice(0, BATCH_SIZE);
    console.log(`\n⚙️ Đang chuẩn bị đóng ${batch.length} tài khoản trong lô này...`);
    
    const instructions = createPriorityFeeInstructions(PRIORITY_FEE_SOL);

    for (const item of batch) {
        const info = item.account.data.parsed.info;
        const tokenAccountPubkey = item.pubkey;
        
        // Nếu tài khoản còn lại một ít token "rác" (dust), tạo chỉ thị để đốt chúng.
        if (Number(info.tokenAmount.amount) > 0) {
            console.log(`   - Đốt token rác trong tài khoản: ${tokenAccountPubkey.toBase58()}`);
            instructions.push(
                createBurnCheckedInstruction(
                    tokenAccountPubkey,
                    new PublicKey(info.mint),
                    signer.publicKey,
                    Number(info.tokenAmount.amount),
                    info.tokenAmount.decimals
                )
            );
        }
        
        // Tạo chỉ thị để đóng tài khoản.
        instructions.push(
            createCloseAccountInstruction(
                tokenAccountPubkey,
                signer.publicKey, // Địa chỉ nhận lại SOL từ phí rent
                signer.publicKey // Chủ sở hữu tài khoản
            )
        );
    }
    
    // Gửi giao dịch
    try {
        const transaction = new Transaction().add(...instructions);
        const signature = await sendAndConfirmTransaction(connection, transaction, [signer]);
        console.log(`✅ Giao dịch thành công!`);
        console.log(`   - Chữ ký (Signature): ${signature}`);
        console.log(`   - Xem trên Solscan: https://solscan.io/tx/${signature}`);
        return batch.length; // Trả về số lượng đã xử lý
    } catch (error) {
        console.error("❌ Giao dịch thất bại:", error.message);
        return -1; // Báo hiệu có lỗi
    }
}


// --- PHẦN 5: HÀM CHẠY CHÍNH (MAIN EXECUTION) ---

async function main() {
    console.log("========================================");
    console.log("==  Solana Token Account Cleaner      ==");
    console.log("========================================");

    if (!PRIVATE_KEY || PRIVATE_KEY === "YOUR_FULL_BASE58_PRIVATE_KEY_HERE") {
        console.error("\nLỖI: Vui lòng thay thế 'YOUR_FULL_BASE58_PRIVATE_KEY_HERE' bằng private key thật của bạn.");
        return;
    }

    let signer;
    try {
        signer = Keypair.fromSecretKey(bs58.decode(PRIVATE_KEY));
        console.log(`\n🔑 Đã tải ví thành công. Địa chỉ ví: ${signer.publicKey.toBase58()}`);
    } catch (error) {
        console.error("\nLỖI: Private key không hợp lệ. Vui lòng kiểm tra lại. Lỗi chi tiết:", error.message);
        return;
    }

    // Vòng lặp để xử lý tất cả các tài khoản theo từng lô
    while (true) {
        const processedCount = await closeTokenAccountsBatch(signer);
        
        if (processedCount === 0) {
            // Không còn tài khoản nào, kết thúc
            console.log("\n🎉 Đã dọn dẹp xong tất cả tài khoản! 🎉");
            break;
        } else if (processedCount < 0) {
            // Gặp lỗi, dừng lại
            console.log("\n🛑 Đã xảy ra lỗi. Dừng chương trình.");
            break;
        }

        // Nếu vẫn còn tài khoản để xử lý, chờ một chút rồi tiếp tục
        console.log(`\n...Chờ ${DELAY_BETWEEN_BATCHES_MS / 1000} giây trước khi xử lý lô tiếp theo...`);
        await sleep(DELAY_BETWEEN_BATCHES_MS);
    }
}

// Bắt đầu chạy chương trình
main().catch(err => {
    console.error("Đã xảy ra lỗi không mong muốn:", err);
});