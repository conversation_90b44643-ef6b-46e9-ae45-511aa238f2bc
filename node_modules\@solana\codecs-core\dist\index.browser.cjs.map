{"version": 3, "sources": ["../src/bytes.ts", "../src/codec.ts", "../src/combine-codec.ts", "../src/add-codec-sentinel.ts", "../src/assertions.ts", "../src/add-codec-size-prefix.ts", "../src/fix-codec-size.ts", "../src/offset-codec.ts", "../src/resize-codec.ts", "../src/pad-codec.ts", "../src/reverse-codec.ts", "../src/transform-codec.ts"], "names": ["SolanaError", "SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH", "SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH", "SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH", "SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH", "SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH", "SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL", "SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES", "SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY", "SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH", "SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE", "SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH"], "mappings": ";;;;;;;AAMa,IAAA,UAAA,GAAa,CAAC,UAAyC,KAAA;AAChE,EAAA,MAAM,kBAAqB,GAAA,UAAA,CAAW,MAAO,CAAA,CAAA,GAAA,KAAO,IAAI,MAAM,CAAA,CAAA;AAC9D,EAAI,IAAA,kBAAA,CAAmB,WAAW,CAAG,EAAA;AACjC,IAAA,OAAO,WAAW,MAAS,GAAA,UAAA,CAAW,CAAC,CAAA,GAAI,IAAI,UAAW,EAAA,CAAA;AAAA,GAC9D;AAEA,EAAI,IAAA,kBAAA,CAAmB,WAAW,CAAG,EAAA;AACjC,IAAA,OAAO,mBAAmB,CAAC,CAAA,CAAA;AAAA,GAC/B;AAEA,EAAM,MAAA,WAAA,GAAc,mBAAmB,MAAO,CAAA,CAAC,OAAO,GAAQ,KAAA,KAAA,GAAQ,GAAI,CAAA,MAAA,EAAQ,CAAC,CAAA,CAAA;AACnF,EAAM,MAAA,MAAA,GAAS,IAAI,UAAA,CAAW,WAAW,CAAA,CAAA;AACzC,EAAA,IAAI,MAAS,GAAA,CAAA,CAAA;AACb,EAAA,kBAAA,CAAmB,QAAQ,CAAO,GAAA,KAAA;AAC9B,IAAO,MAAA,CAAA,GAAA,CAAI,KAAK,MAAM,CAAA,CAAA;AACtB,IAAA,MAAA,IAAU,GAAI,CAAA,MAAA,CAAA;AAAA,GACjB,CAAA,CAAA;AACD,EAAO,OAAA,MAAA,CAAA;AACX,EAAA;AAMa,IAAA,QAAA,GAAW,CAAC,KAAA,EAAwC,MAAoD,KAAA;AACjH,EAAI,IAAA,KAAA,CAAM,MAAU,IAAA,MAAA,EAAe,OAAA,KAAA,CAAA;AACnC,EAAA,MAAM,cAAc,IAAI,UAAA,CAAW,MAAM,CAAA,CAAE,KAAK,CAAC,CAAA,CAAA;AACjD,EAAA,WAAA,CAAY,IAAI,KAAK,CAAA,CAAA;AACrB,EAAO,OAAA,WAAA,CAAA;AACX,EAAA;AAOO,IAAM,QAAW,GAAA,CAAC,KAAwC,EAAA,MAAA,KAC7D,SAAS,KAAM,CAAA,MAAA,IAAU,MAAS,GAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,CAAG,EAAA,MAAM,GAAG,MAAM,EAAA;AAMrE,SAAS,aAAA,CACZ,IACA,EAAA,KAAA,EACA,MACO,EAAA;AACP,EAAA,MAAM,KAAQ,GAAA,MAAA,KAAW,CAAK,IAAA,IAAA,CAAK,MAAW,KAAA,KAAA,CAAM,MAAS,GAAA,IAAA,GAAO,IAAK,CAAA,KAAA,CAAM,MAAQ,EAAA,MAAA,GAAS,MAAM,MAAM,CAAA,CAAA;AAC5G,EAAA,IAAI,KAAM,CAAA,MAAA,KAAW,KAAM,CAAA,MAAA,EAAe,OAAA,KAAA,CAAA;AAC1C,EAAO,OAAA,KAAA,CAAM,MAAM,CAAC,CAAA,EAAG,MAAM,CAAM,KAAA,KAAA,CAAM,CAAC,CAAC,CAAA,CAAA;AAC/C,CAAA;AC8BO,SAAS,cAAA,CACZ,OACA,OACM,EAAA;AACN,EAAA,OAAO,eAAe,OAAU,GAAA,OAAA,CAAQ,SAAY,GAAA,OAAA,CAAQ,iBAAiB,KAAK,CAAA,CAAA;AACtF,CAAA;AAUO,SAAS,cACZ,OACc,EAAA;AACd,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,QAAQ,CAAS,KAAA,KAAA;AACb,MAAA,MAAM,QAAQ,IAAI,UAAA,CAAW,cAAe,CAAA,KAAA,EAAO,OAAO,CAAC,CAAA,CAAA;AAC3D,MAAQ,OAAA,CAAA,KAAA,CAAM,KAAO,EAAA,KAAA,EAAO,CAAC,CAAA,CAAA;AAC7B,MAAO,OAAA,KAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAUO,SAAS,cACZ,OACY,EAAA;AACZ,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,MAAA,EAAQ,CAAC,KAAA,EAAO,MAAS,GAAA,CAAA,KAAM,QAAQ,IAAK,CAAA,KAAA,EAAO,MAAM,CAAA,CAAE,CAAC,CAAA;AAAA,GAC/D,CAAA,CAAA;AACL,CAAA;AAcO,SAAS,YACZ,KAGiB,EAAA;AACjB,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACjB,GAAG,KAAA;AAAA,IACH,MAAA,EAAQ,CAAC,KAAA,EAAO,MAAS,GAAA,CAAA,KAAM,MAAM,IAAK,CAAA,KAAA,EAAO,MAAM,CAAA,CAAE,CAAC,CAAA;AAAA,IAC1D,QAAQ,CAAS,KAAA,KAAA;AACb,MAAA,MAAM,QAAQ,IAAI,UAAA,CAAW,cAAe,CAAA,KAAA,EAAO,KAAK,CAAC,CAAA,CAAA;AACzD,MAAM,KAAA,CAAA,KAAA,CAAM,KAAO,EAAA,KAAA,EAAO,CAAC,CAAA,CAAA;AAC3B,MAAO,OAAA,KAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAcO,SAAS,YAAY,KAAqF,EAAA;AAC7G,EAAA,OAAO,WAAe,IAAA,KAAA,IAAS,OAAO,KAAA,CAAM,SAAc,KAAA,QAAA,CAAA;AAC9D,CAAA;AAcO,SAAS,kBACZ,KACsC,EAAA;AACtC,EAAI,IAAA,CAAC,WAAY,CAAA,KAAK,CAAG,EAAA;AACrB,IAAM,MAAA,IAAIA,mBAAYC,kDAA2C,CAAA,CAAA;AAAA,GACrE;AACJ,CAAA;AAQO,SAAS,eAAe,KAAoF,EAAA;AAC/G,EAAO,OAAA,CAAC,YAAY,KAAK,CAAA,CAAA;AAC7B,CAAA;AAUO,SAAS,qBACZ,KACqC,EAAA;AACrC,EAAI,IAAA,CAAC,cAAe,CAAA,KAAK,CAAG,EAAA;AACxB,IAAM,MAAA,IAAID,mBAAYE,qDAA8C,CAAA,CAAA;AAAA,GACxE;AACJ,CAAA;ACvLO,SAAS,YAAA,CACZ,SACA,OACiB,EAAA;AACjB,EAAA,IAAI,WAAY,CAAA,OAAO,CAAM,KAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AAC/C,IAAM,MAAA,IAAIF,mBAAYG,wEAAiE,CAAA,CAAA;AAAA,GAC3F;AAEA,EAAI,IAAA,WAAA,CAAY,OAAO,CAAK,IAAA,WAAA,CAAY,OAAO,CAAK,IAAA,OAAA,CAAQ,SAAc,KAAA,OAAA,CAAQ,SAAW,EAAA;AACzF,IAAM,MAAA,IAAIH,mBAAYI,gEAA2D,EAAA;AAAA,MAC7E,kBAAkB,OAAQ,CAAA,SAAA;AAAA,MAC1B,kBAAkB,OAAQ,CAAA,SAAA;AAAA,KAC7B,CAAA,CAAA;AAAA,GACL;AAEA,EAAI,IAAA,CAAC,WAAY,CAAA,OAAO,CAAK,IAAA,CAAC,WAAY,CAAA,OAAO,CAAK,IAAA,OAAA,CAAQ,OAAY,KAAA,OAAA,CAAQ,OAAS,EAAA;AACvF,IAAM,MAAA,IAAIJ,mBAAYK,8DAAyD,EAAA;AAAA,MAC3E,gBAAgB,OAAQ,CAAA,OAAA;AAAA,MACxB,gBAAgB,OAAQ,CAAA,OAAA;AAAA,KAC3B,CAAA,CAAA;AAAA,GACL;AAEA,EAAO,OAAA;AAAA,IACH,GAAG,OAAA;AAAA,IACH,GAAG,OAAA;AAAA,IACH,QAAQ,OAAQ,CAAA,MAAA;AAAA,IAChB,QAAQ,OAAQ,CAAA,MAAA;AAAA,IAChB,MAAM,OAAQ,CAAA,IAAA;AAAA,IACd,OAAO,OAAQ,CAAA,KAAA;AAAA,GACnB,CAAA;AACJ,CAAA;;;AC7BO,SAAS,kBAAA,CAA0B,SAAyB,QAA8C,EAAA;AAC7G,EAAA,MAAM,KAAS,GAAA,CAAC,KAAO,EAAA,KAAA,EAAO,MAAW,KAAA;AAIrC,IAAM,MAAA,YAAA,GAAe,OAAQ,CAAA,MAAA,CAAO,KAAK,CAAA,CAAA;AACzC,IAAA,IAAI,iBAAkB,CAAA,YAAA,EAAc,QAAQ,CAAA,IAAK,CAAG,EAAA;AAChD,MAAM,MAAA,IAAIL,mBAAYM,oEAA+D,EAAA;AAAA,QACjF,YAAc,EAAA,YAAA;AAAA,QACd,eAAA,EAAiB,SAAS,YAAY,CAAA;AAAA,QACtC,WAAA,EAAa,SAAS,QAAQ,CAAA;AAAA,QAC9B,QAAA;AAAA,OACH,CAAA,CAAA;AAAA,KACL;AACA,IAAM,KAAA,CAAA,GAAA,CAAI,cAAc,MAAM,CAAA,CAAA;AAC9B,IAAA,MAAA,IAAU,YAAa,CAAA,MAAA,CAAA;AACvB,IAAM,KAAA,CAAA,GAAA,CAAI,UAAU,MAAM,CAAA,CAAA;AAC1B,IAAA,MAAA,IAAU,QAAS,CAAA,MAAA,CAAA;AACnB,IAAO,OAAA,MAAA,CAAA;AAAA,GACX,CAAA;AAEA,EAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,IAAO,OAAA,aAAA,CAAc,EAAE,GAAG,OAAS,EAAA,SAAA,EAAW,QAAQ,SAAY,GAAA,QAAA,CAAS,MAAQ,EAAA,KAAA,EAAO,CAAA,CAAA;AAAA,GAC9F;AAEA,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,GAAI,OAAQ,CAAA,OAAA,IAAW,IAAO,GAAA,EAAE,OAAS,EAAA,OAAA,CAAQ,OAAU,GAAA,QAAA,CAAS,MAAO,EAAA,GAAI,EAAC;AAAA,IAChF,kBAAkB,CAAS,KAAA,KAAA,OAAA,CAAQ,gBAAiB,CAAA,KAAK,IAAI,QAAS,CAAA,MAAA;AAAA,IACtE,KAAA;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAYO,SAAS,kBAAA,CAAwB,SAAuB,QAA4C,EAAA;AACvG,EAAM,MAAA,IAAA,GAAQ,CAAC,KAAA,EAAO,MAAW,KAAA;AAC7B,IAAA,MAAM,iBAAiB,MAAW,KAAA,CAAA,GAAI,KAAQ,GAAA,KAAA,CAAM,MAAM,MAAM,CAAA,CAAA;AAChE,IAAM,MAAA,aAAA,GAAgB,iBAAkB,CAAA,cAAA,EAAgB,QAAQ,CAAA,CAAA;AAChE,IAAA,IAAI,kBAAkB,CAAI,CAAA,EAAA;AACtB,MAAM,MAAA,IAAIN,mBAAYO,8DAAyD,EAAA;AAAA,QAC3E,YAAc,EAAA,cAAA;AAAA,QACd,eAAA,EAAiB,SAAS,cAAc,CAAA;AAAA,QACxC,WAAA,EAAa,SAAS,QAAQ,CAAA;AAAA,QAC9B,QAAA;AAAA,OACH,CAAA,CAAA;AAAA,KACL;AACA,IAAA,MAAM,gBAAmB,GAAA,cAAA,CAAe,KAAM,CAAA,CAAA,EAAG,aAAa,CAAA,CAAA;AAI9D,IAAO,OAAA,CAAC,QAAQ,MAAO,CAAA,gBAAgB,GAAG,MAAS,GAAA,gBAAA,CAAiB,MAAS,GAAA,QAAA,CAAS,MAAM,CAAA,CAAA;AAAA,GAChG,CAAA;AAEA,EAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,IAAO,OAAA,aAAA,CAAc,EAAE,GAAG,OAAS,EAAA,SAAA,EAAW,QAAQ,SAAY,GAAA,QAAA,CAAS,MAAQ,EAAA,IAAA,EAAM,CAAA,CAAA;AAAA,GAC7F;AAEA,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,GAAI,OAAQ,CAAA,OAAA,IAAW,IAAO,GAAA,EAAE,OAAS,EAAA,OAAA,CAAQ,OAAU,GAAA,QAAA,CAAS,MAAO,EAAA,GAAI,EAAC;AAAA,IAChF,IAAA;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAiBO,SAAS,gBAAA,CACZ,OACA,QACiB,EAAA;AACjB,EAAO,OAAA,YAAA,CAAa,mBAAmB,KAAO,EAAA,QAAQ,GAAG,kBAAmB,CAAA,KAAA,EAAO,QAAQ,CAAC,CAAA,CAAA;AAChG,CAAA;AAEA,SAAS,iBAAA,CAAkB,OAA2B,QAA8B,EAAA;AAChF,EAAA,OAAO,KAAM,CAAA,SAAA,CAAU,CAAC,IAAA,EAAM,OAAO,GAAQ,KAAA;AACzC,IAAA,IAAI,SAAS,MAAW,KAAA,CAAA,EAAU,OAAA,IAAA,KAAS,SAAS,CAAC,CAAA,CAAA;AACrD,IAAO,OAAA,aAAA,CAAc,GAAK,EAAA,QAAA,EAAU,KAAK,CAAA,CAAA;AAAA,GAC5C,CAAA,CAAA;AACL,CAAA;AAEA,SAAS,SAAS,KAAmC,EAAA;AACjD,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,SAAS,GAAM,GAAA,IAAA,CAAK,QAAS,CAAA,EAAE,CAAE,CAAA,QAAA,CAAS,CAAG,EAAA,GAAG,GAAG,EAAE,CAAA,CAAA;AACnF,CAAA;AClIO,SAAS,iCACZ,CAAA,gBAAA,EACA,KACA,EAAA,MAAA,GAAS,CACX,EAAA;AACE,EAAI,IAAA,KAAA,CAAM,MAAS,GAAA,MAAA,IAAU,CAAG,EAAA;AAC5B,IAAM,MAAA,IAAIP,mBAAYQ,2DAAsD,EAAA;AAAA,MACxE,gBAAA;AAAA,KACH,CAAA,CAAA;AAAA,GACL;AACJ,CAAA;AAKO,SAAS,qCACZ,CAAA,gBAAA,EACA,QACA,EAAA,KAAA,EACA,SAAS,CACX,EAAA;AACE,EAAM,MAAA,WAAA,GAAc,MAAM,MAAS,GAAA,MAAA,CAAA;AACnC,EAAA,IAAI,cAAc,QAAU,EAAA;AACxB,IAAM,MAAA,IAAIR,mBAAYS,gDAA2C,EAAA;AAAA,MAC7D,WAAA;AAAA,MACA,gBAAA;AAAA,MACA,QAAA;AAAA,KACH,CAAA,CAAA;AAAA,GACL;AACJ,CAAA;AAQO,SAAS,oCAAA,CAAqC,gBAA0B,EAAA,MAAA,EAAgB,WAAqB,EAAA;AAChH,EAAI,IAAA,MAAA,GAAS,CAAK,IAAA,MAAA,GAAS,WAAa,EAAA;AACpC,IAAM,MAAA,IAAIT,mBAAYU,gDAA2C,EAAA;AAAA,MAC7D,WAAA;AAAA,MACA,gBAAA;AAAA,MACA,MAAA;AAAA,KACH,CAAA,CAAA;AAAA,GACL;AACJ,CAAA;;;AClBO,SAAS,oBAAA,CAA4B,SAAyB,MAAuC,EAAA;AACxG,EAAA,MAAM,KAAS,GAAA,CAAC,KAAO,EAAA,KAAA,EAAO,MAAW,KAAA;AAGrC,IAAM,MAAA,YAAA,GAAe,OAAQ,CAAA,MAAA,CAAO,KAAK,CAAA,CAAA;AACzC,IAAA,MAAA,GAAS,MAAO,CAAA,KAAA,CAAM,YAAa,CAAA,MAAA,EAAQ,OAAO,MAAM,CAAA,CAAA;AACxD,IAAM,KAAA,CAAA,GAAA,CAAI,cAAc,MAAM,CAAA,CAAA;AAC9B,IAAA,OAAO,SAAS,YAAa,CAAA,MAAA,CAAA;AAAA,GACjC,CAAA;AAEA,EAAA,IAAI,WAAY,CAAA,MAAM,CAAK,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AAC7C,IAAO,OAAA,aAAA,CAAc,EAAE,GAAG,OAAS,EAAA,SAAA,EAAW,OAAO,SAAY,GAAA,OAAA,CAAQ,SAAW,EAAA,KAAA,EAAO,CAAA,CAAA;AAAA,GAC/F;AAEA,EAAA,MAAM,gBAAgB,WAAY,CAAA,MAAM,IAAI,MAAO,CAAA,SAAA,GAAa,OAAO,OAAW,IAAA,IAAA,CAAA;AAClF,EAAA,MAAM,iBAAiB,WAAY,CAAA,OAAO,IAAI,OAAQ,CAAA,SAAA,GAAa,QAAQ,OAAW,IAAA,IAAA,CAAA;AACtF,EAAA,MAAM,UAAU,aAAkB,KAAA,IAAA,IAAQ,cAAmB,KAAA,IAAA,GAAO,gBAAgB,cAAiB,GAAA,IAAA,CAAA;AAErG,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,GAAI,OAAY,KAAA,IAAA,GAAO,EAAE,OAAA,KAAY,EAAC;AAAA,IACtC,kBAAkB,CAAS,KAAA,KAAA;AACvB,MAAM,MAAA,WAAA,GAAc,cAAe,CAAA,KAAA,EAAO,OAAO,CAAA,CAAA;AACjD,MAAO,OAAA,cAAA,CAAe,WAAa,EAAA,MAAM,CAAI,GAAA,WAAA,CAAA;AAAA,KACjD;AAAA,IACA,KAAA;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAUO,SAAS,oBAAA,CAA0B,SAAuB,MAAqC,EAAA;AAClG,EAAM,MAAA,IAAA,GAAQ,CAAC,KAAA,EAAO,MAAW,KAAA;AAC7B,IAAA,MAAM,CAAC,UAAY,EAAA,aAAa,IAAI,MAAO,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAA;AAC7D,IAAM,MAAA,IAAA,GAAO,OAAO,UAAU,CAAA,CAAA;AAC9B,IAAS,MAAA,GAAA,aAAA,CAAA;AAET,IAAA,IAAI,MAAS,GAAA,CAAA,IAAK,KAAM,CAAA,MAAA,GAAS,IAAM,EAAA;AACnC,MAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,MAAQ,EAAA,MAAA,GAAS,IAAI,CAAA,CAAA;AAAA,KAC7C;AACA,IAAsC,qCAAA,CAAA,sBAAA,EAAwB,MAAM,KAAK,CAAA,CAAA;AAGzE,IAAA,OAAO,CAAC,OAAQ,CAAA,MAAA,CAAO,KAAK,CAAA,EAAG,SAAS,IAAI,CAAA,CAAA;AAAA,GAChD,CAAA;AAEA,EAAA,IAAI,WAAY,CAAA,MAAM,CAAK,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AAC7C,IAAO,OAAA,aAAA,CAAc,EAAE,GAAG,OAAS,EAAA,SAAA,EAAW,OAAO,SAAY,GAAA,OAAA,CAAQ,SAAW,EAAA,IAAA,EAAM,CAAA,CAAA;AAAA,GAC9F;AAEA,EAAA,MAAM,gBAAgB,WAAY,CAAA,MAAM,IAAI,MAAO,CAAA,SAAA,GAAa,OAAO,OAAW,IAAA,IAAA,CAAA;AAClF,EAAA,MAAM,iBAAiB,WAAY,CAAA,OAAO,IAAI,OAAQ,CAAA,SAAA,GAAa,QAAQ,OAAW,IAAA,IAAA,CAAA;AACtF,EAAA,MAAM,UAAU,aAAkB,KAAA,IAAA,IAAQ,cAAmB,KAAA,IAAA,GAAO,gBAAgB,cAAiB,GAAA,IAAA,CAAA;AACrG,EAAA,OAAO,aAAc,CAAA,EAAE,GAAG,OAAA,EAAS,GAAI,OAAA,KAAY,IAAO,GAAA,EAAE,OAAQ,EAAA,GAAI,EAAC,EAAI,MAAM,CAAA,CAAA;AACvF,CAAA;AAaO,SAAS,kBAAA,CACZ,OACA,MACiB,EAAA;AACjB,EAAO,OAAA,YAAA,CAAa,qBAAqB,KAAO,EAAA,MAAM,GAAG,oBAAqB,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA,CAAA;AAChG,CAAA;;;AC/FO,SAAS,cAAA,CACZ,SACA,UAC8B,EAAA;AAC9B,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,UAAA;AAAA,IACX,KAAO,EAAA,CAAC,KAAc,EAAA,KAAA,EAAmB,MAAmB,KAAA;AAIxD,MAAM,MAAA,iBAAA,GAAoB,OAAQ,CAAA,MAAA,CAAO,KAAK,CAAA,CAAA;AAC9C,MAAM,MAAA,cAAA,GACF,kBAAkB,MAAS,GAAA,UAAA,GAAa,kBAAkB,KAAM,CAAA,CAAA,EAAG,UAAU,CAAI,GAAA,iBAAA,CAAA;AACrF,MAAM,KAAA,CAAA,GAAA,CAAI,gBAAgB,MAAM,CAAA,CAAA;AAChC,MAAA,OAAO,MAAS,GAAA,UAAA,CAAA;AAAA,KACpB;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAQO,SAAS,cAAA,CACZ,SACA,UAC4B,EAAA;AAC5B,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,UAAA;AAAA,IACX,IAAA,EAAM,CAAC,KAAA,EAAO,MAAW,KAAA;AACrB,MAAsC,qCAAA,CAAA,cAAA,EAAgB,UAAY,EAAA,KAAA,EAAO,MAAM,CAAA,CAAA;AAE/E,MAAA,IAAI,MAAS,GAAA,CAAA,IAAK,KAAM,CAAA,MAAA,GAAS,UAAY,EAAA;AACzC,QAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,MAAQ,EAAA,MAAA,GAAS,UAAU,CAAA,CAAA;AAAA,OACnD;AAEA,MAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,QAAQ,KAAA,GAAA,QAAA,CAAS,KAAO,EAAA,OAAA,CAAQ,SAAS,CAAA,CAAA;AAAA,OAC7C;AAEA,MAAA,MAAM,CAAC,KAAK,CAAA,GAAI,OAAQ,CAAA,IAAA,CAAK,OAAO,CAAC,CAAA,CAAA;AACrC,MAAO,OAAA,CAAC,KAAO,EAAA,MAAA,GAAS,UAAU,CAAA,CAAA;AAAA,KACtC;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAQO,SAAS,YAAA,CACZ,OACA,UACiC,EAAA;AACjC,EAAO,OAAA,YAAA,CAAa,eAAe,KAAO,EAAA,UAAU,GAAG,cAAe,CAAA,KAAA,EAAO,UAAU,CAAC,CAAA,CAAA;AAC5F,CAAA;;;AC1CO,SAAS,aAAA,CAA2C,SAAmB,MAAgC,EAAA;AAC1G,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,KAAO,EAAA,CAAC,KAAO,EAAA,KAAA,EAAO,SAAc,KAAA;AAChC,MAAA,MAAM,YAAY,CAAC,MAAA,KAAmB,MAAO,CAAA,MAAA,EAAQ,MAAM,MAAM,CAAA,CAAA;AACjE,MAAM,MAAA,YAAA,GAAe,MAAO,CAAA,SAAA,GAAY,MAAO,CAAA,SAAA,CAAU,EAAE,KAAO,EAAA,SAAA,EAAW,SAAU,EAAC,CAAI,GAAA,SAAA,CAAA;AAC5F,MAAqC,oCAAA,CAAA,eAAA,EAAiB,YAAc,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAChF,MAAA,MAAM,UAAa,GAAA,OAAA,CAAQ,KAAM,CAAA,KAAA,EAAO,OAAO,YAAY,CAAA,CAAA;AAC3D,MAAA,MAAM,aAAgB,GAAA,MAAA,CAAO,UACvB,GAAA,MAAA,CAAO,UAAW,CAAA,EAAE,KAAO,EAAA,YAAA,EAAc,UAAY,EAAA,SAAA,EAAW,SAAU,EAAC,CAC3E,GAAA,UAAA,CAAA;AACN,MAAqC,oCAAA,CAAA,eAAA,EAAiB,aAAe,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AACjF,MAAO,OAAA,aAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAKO,SAAS,aAAA,CAA2C,SAAmB,MAAgC,EAAA;AAC1G,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,IAAA,EAAM,CAAC,KAAA,EAAO,SAAc,KAAA;AACxB,MAAA,MAAM,YAAY,CAAC,MAAA,KAAmB,MAAO,CAAA,MAAA,EAAQ,MAAM,MAAM,CAAA,CAAA;AACjE,MAAM,MAAA,YAAA,GAAe,MAAO,CAAA,SAAA,GAAY,MAAO,CAAA,SAAA,CAAU,EAAE,KAAO,EAAA,SAAA,EAAW,SAAU,EAAC,CAAI,GAAA,SAAA,CAAA;AAC5F,MAAqC,oCAAA,CAAA,eAAA,EAAiB,YAAc,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAChF,MAAA,MAAM,CAAC,KAAO,EAAA,UAAU,IAAI,OAAQ,CAAA,IAAA,CAAK,OAAO,YAAY,CAAA,CAAA;AAC5D,MAAA,MAAM,aAAgB,GAAA,MAAA,CAAO,UACvB,GAAA,MAAA,CAAO,UAAW,CAAA,EAAE,KAAO,EAAA,YAAA,EAAc,UAAY,EAAA,SAAA,EAAW,SAAU,EAAC,CAC3E,GAAA,UAAA,CAAA;AACN,MAAqC,oCAAA,CAAA,eAAA,EAAiB,aAAe,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AACjF,MAAO,OAAA,CAAC,OAAO,aAAa,CAAA,CAAA;AAAA,KAChC;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAKO,SAAS,WAAA,CAAqC,OAAe,MAA8B,EAAA;AAC9F,EAAO,OAAA,YAAA,CAAa,cAAc,KAAO,EAAA,MAAM,GAAG,aAAc,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA,CAAA;AAClF,CAAA;AAGA,SAAS,MAAA,CAAO,UAAkB,OAAiB,EAAA;AAC/C,EAAI,IAAA,OAAA,KAAY,GAAU,OAAA,CAAA,CAAA;AAC1B,EAAS,OAAA,CAAA,QAAA,GAAW,UAAW,OAAW,IAAA,OAAA,CAAA;AAC9C,CAAA;ACtDO,SAAS,aAAA,CACZ,SACA,MACQ,EAAA;AACR,EAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,IAAM,MAAA,SAAA,GAAY,MAAO,CAAA,OAAA,CAAQ,SAAS,CAAA,CAAA;AAC1C,IAAA,IAAI,YAAY,CAAG,EAAA;AACf,MAAM,MAAA,IAAIV,mBAAYW,0DAAqD,EAAA;AAAA,QACvE,WAAa,EAAA,SAAA;AAAA,QACb,gBAAkB,EAAA,eAAA;AAAA,OACrB,CAAA,CAAA;AAAA,KACL;AACA,IAAA,OAAO,aAAc,CAAA,EAAE,GAAG,OAAA,EAAS,WAAW,CAAA,CAAA;AAAA,GAClD;AACA,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,kBAAkB,CAAS,KAAA,KAAA;AACvB,MAAA,MAAM,OAAU,GAAA,MAAA,CAAO,OAAQ,CAAA,gBAAA,CAAiB,KAAK,CAAC,CAAA,CAAA;AACtD,MAAA,IAAI,UAAU,CAAG,EAAA;AACb,QAAM,MAAA,IAAIX,mBAAYW,0DAAqD,EAAA;AAAA,UACvE,WAAa,EAAA,OAAA;AAAA,UACb,gBAAkB,EAAA,eAAA;AAAA,SACrB,CAAA,CAAA;AAAA,OACL;AACA,MAAO,OAAA,OAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAcO,SAAS,aAAA,CACZ,SACA,MACQ,EAAA;AACR,EAAI,IAAA,WAAA,CAAY,OAAO,CAAG,EAAA;AACtB,IAAM,MAAA,SAAA,GAAY,MAAO,CAAA,OAAA,CAAQ,SAAS,CAAA,CAAA;AAC1C,IAAA,IAAI,YAAY,CAAG,EAAA;AACf,MAAM,MAAA,IAAIX,mBAAYW,0DAAqD,EAAA;AAAA,QACvE,WAAa,EAAA,SAAA;AAAA,QACb,gBAAkB,EAAA,eAAA;AAAA,OACrB,CAAA,CAAA;AAAA,KACL;AACA,IAAA,OAAO,aAAc,CAAA,EAAE,GAAG,OAAA,EAAS,WAAW,CAAA,CAAA;AAAA,GAClD;AACA,EAAO,OAAA,OAAA,CAAA;AACX,CAAA;AAUO,SAAS,WAAA,CAAqC,OAAe,MAA0C,EAAA;AAC1G,EAAO,OAAA,YAAA,CAAa,cAAc,KAAO,EAAA,MAAM,GAAG,aAAc,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA,CAAA;AAClF,CAAA;;;ACtFO,SAAS,cAAA,CAA4C,SAAmB,MAA0B,EAAA;AACrG,EAAO,OAAA,aAAA;AAAA,IACH,aAAc,CAAA,OAAA,EAAS,CAAQ,IAAA,KAAA,IAAA,GAAO,MAAM,CAAA;AAAA,IAC5C,EAAE,SAAW,EAAA,CAAC,EAAE,SAAU,EAAA,KAAM,YAAY,MAAO,EAAA;AAAA,GACvD,CAAA;AACJ,CAAA;AAKO,SAAS,eAAA,CAA6C,SAAmB,MAA0B,EAAA;AACtG,EAAO,OAAA,aAAA;AAAA,IACH,aAAc,CAAA,OAAA,EAAS,CAAQ,IAAA,KAAA,IAAA,GAAO,MAAM,CAAA;AAAA,IAC5C,EAAE,UAAY,EAAA,CAAC,EAAE,UAAW,EAAA,KAAM,aAAa,MAAO,EAAA;AAAA,GAC1D,CAAA;AACJ,CAAA;AAKO,SAAS,cAAA,CAA4C,SAAmB,MAA0B,EAAA;AACrG,EAAO,OAAA,aAAA;AAAA,IACH,aAAc,CAAA,OAAA,EAAS,CAAQ,IAAA,KAAA,IAAA,GAAO,MAAM,CAAA;AAAA,IAC5C,EAAE,SAAW,EAAA,CAAC,EAAE,SAAU,EAAA,KAAM,YAAY,MAAO,EAAA;AAAA,GACvD,CAAA;AACJ,CAAA;AAKO,SAAS,eAAA,CAA6C,SAAmB,MAA0B,EAAA;AACtG,EAAO,OAAA,aAAA;AAAA,IACH,aAAc,CAAA,OAAA,EAAS,CAAQ,IAAA,KAAA,IAAA,GAAO,MAAM,CAAA;AAAA,IAC5C,EAAE,UAAY,EAAA,CAAC,EAAE,UAAW,EAAA,KAAM,aAAa,MAAO,EAAA;AAAA,GAC1D,CAAA;AACJ,CAAA;AAKO,SAAS,YAAA,CAAsC,OAAe,MAAwB,EAAA;AACzF,EAAO,OAAA,YAAA,CAAa,eAAe,KAAO,EAAA,MAAM,GAAG,cAAe,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA,CAAA;AACpF,CAAA;AAKO,SAAS,aAAA,CAAuC,OAAe,MAAwB,EAAA;AAC1F,EAAO,OAAA,YAAA,CAAa,gBAAgB,KAAO,EAAA,MAAM,GAAG,eAAgB,CAAA,KAAA,EAAO,MAAM,CAAC,CAAA,CAAA;AACtF,CAAA;;;ACrDA,SAAS,4BACL,MACA,EAAA,kBAAA,EACA,YACA,EAAA,YAAA,EACA,eAAuB,CACzB,EAAA;AACE,EAAO,OAAA,YAAA,GAAe,EAAE,YAAc,EAAA;AAClC,IAAM,MAAA,SAAA,GAAY,OAAO,YAAY,CAAA,CAAA;AACrC,IAAA,kBAAA,CAAmB,YAAe,GAAA,YAAY,CAAI,GAAA,MAAA,CAAO,YAAY,CAAA,CAAA;AACrE,IAAmB,kBAAA,CAAA,YAAA,GAAe,YAAY,CAAI,GAAA,SAAA,CAAA;AAClD,IAAA,YAAA,EAAA,CAAA;AAAA,GACJ;AACA,EAAA,IAAI,iBAAiB,YAAc,EAAA;AAC/B,IAAA,kBAAA,CAAmB,YAAe,GAAA,YAAY,CAAI,GAAA,MAAA,CAAO,YAAY,CAAA,CAAA;AAAA,GACzE;AACJ,CAAA;AAKO,SAAS,eACZ,OAC8B,EAAA;AAC9B,EAAA,iBAAA,CAAkB,OAAO,CAAA,CAAA;AACzB,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,KAAO,EAAA,CAAC,KAAc,EAAA,KAAA,EAAO,MAAW,KAAA;AACpC,MAAA,MAAM,SAAY,GAAA,OAAA,CAAQ,KAAM,CAAA,KAAA,EAAO,OAAO,MAAM,CAAA,CAAA;AACpD,MAAA,2BAAA;AAAA,QACI,KAAA;AAAA,QACA,KAAA;AAAA,QACA,MAAA;AAAA,QACA,SAAS,OAAQ,CAAA,SAAA;AAAA,OACrB,CAAA;AACA,MAAO,OAAA,SAAA,CAAA;AAAA,KACX;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAKO,SAAS,eACZ,OAC4B,EAAA;AAC5B,EAAA,iBAAA,CAAkB,OAAO,CAAA,CAAA;AACzB,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,IAAA,EAAM,CAAC,KAAA,EAAO,MAAW,KAAA;AACrB,MAAM,MAAA,aAAA,GAAgB,MAAM,KAAM,EAAA,CAAA;AAClC,MAAA,2BAAA;AAAA,QACI,KAAA;AAAA,QACA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,SAAS,OAAQ,CAAA,SAAA;AAAA,OACrB,CAAA;AACA,MAAO,OAAA,OAAA,CAAQ,IAAK,CAAA,aAAA,EAAe,MAAM,CAAA,CAAA;AAAA,KAC7C;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAKO,SAAS,aACZ,KACiC,EAAA;AACjC,EAAA,OAAO,aAAa,cAAe,CAAA,KAAK,CAAG,EAAA,cAAA,CAAe,KAAK,CAAC,CAAA,CAAA;AACpE,CAAA;;;AChDO,SAAS,gBAAA,CACZ,SACA,KACiB,EAAA;AACjB,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAI,cAAe,CAAA,OAAO,CACpB,GAAA,EAAE,GAAG,OAAS,EAAA,gBAAA,EAAkB,CAAC,KAAA,KAAoB,QAAQ,gBAAiB,CAAA,KAAA,CAAM,KAAK,CAAC,GAC1F,GAAA,OAAA;AAAA,IACN,KAAA,EAAO,CAAC,KAAA,EAAiB,KAAO,EAAA,MAAA,KAAW,OAAQ,CAAA,KAAA,CAAM,KAAM,CAAA,KAAK,CAAG,EAAA,KAAA,EAAO,MAAM,CAAA;AAAA,GACvF,CAAA,CAAA;AACL,CAAA;AAiBO,SAAS,gBAAA,CACZ,SACA,GACe,EAAA;AACf,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAG,OAAA;AAAA,IACH,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AACtD,MAAA,MAAM,CAAC,KAAO,EAAA,SAAS,IAAI,OAAQ,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAA;AACrD,MAAA,OAAO,CAAC,GAAI,CAAA,KAAA,EAAO,KAAO,EAAA,MAAM,GAAG,SAAS,CAAA,CAAA;AAAA,KAChD;AAAA,GACH,CAAA,CAAA;AACL,CAAA;AAsCO,SAAS,cAAA,CACZ,KACA,EAAA,KAAA,EACA,GACuB,EAAA;AACvB,EAAA,OAAO,WAAY,CAAA;AAAA,IACf,GAAG,gBAAiB,CAAA,KAAA,EAAO,KAAK,CAAA;AAAA,IAChC,MAAM,GAAM,GAAA,gBAAA,CAAiB,OAAO,GAAG,CAAA,CAAE,OAAQ,KAAM,CAAA,IAAA;AAAA,GAC1D,CAAA,CAAA;AACL", "file": "index.browser.cjs", "sourcesContent": ["import { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Concatenates an array of `Uint8Array`s into a single `Uint8Array`.\n * Reuses the original byte array when applicable.\n */\nexport const mergeBytes = (byteArrays: Uint8Array[]): Uint8Array => {\n    const nonEmptyByteArrays = byteArrays.filter(arr => arr.length);\n    if (nonEmptyByteArrays.length === 0) {\n        return byteArrays.length ? byteArrays[0] : new Uint8Array();\n    }\n\n    if (nonEmptyByteArrays.length === 1) {\n        return nonEmptyByteArrays[0];\n    }\n\n    const totalLength = nonEmptyByteArrays.reduce((total, arr) => total + arr.length, 0);\n    const result = new Uint8Array(totalLength);\n    let offset = 0;\n    nonEmptyByteArrays.forEach(arr => {\n        result.set(arr, offset);\n        offset += arr.length;\n    });\n    return result;\n};\n\n/**\n * Pads a `Uint8Array` with zeroes to the specified length.\n * If the array is longer than the specified length, it is returned as-is.\n */\nexport const padBytes = (bytes: ReadonlyUint8Array | Uint8Array, length: number): ReadonlyUint8Array | Uint8Array => {\n    if (bytes.length >= length) return bytes;\n    const paddedBytes = new Uint8Array(length).fill(0);\n    paddedBytes.set(bytes);\n    return paddedBytes;\n};\n\n/**\n * Fixes a `Uint8Array` to the specified length.\n * If the array is longer than the specified length, it is truncated.\n * If the array is shorter than the specified length, it is padded with zeroes.\n */\nexport const fixBytes = (bytes: ReadonlyUint8Array | Uint8Array, length: number): ReadonlyUint8Array | Uint8Array =>\n    padBytes(bytes.length <= length ? bytes : bytes.slice(0, length), length);\n\n/**\n * Returns true if and only if the provided `data` byte array contains\n * the provided `bytes` byte array at the specified `offset`.\n */\nexport function containsBytes(\n    data: ReadonlyUint8Array | Uint8Array,\n    bytes: ReadonlyUint8Array | Uint8Array,\n    offset: number,\n): boolean {\n    const slice = offset === 0 && data.length === bytes.length ? data : data.slice(offset, offset + bytes.length);\n    if (slice.length !== bytes.length) return false;\n    return bytes.every((b, i) => b === slice[i]);\n}\n", "import {\n    SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH,\n    SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH,\n    SolanaError,\n} from '@solana/errors';\n\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Defines an offset in bytes.\n */\nexport type Offset = number;\n\ntype BaseEncoder<TFrom> = {\n    /** Encode the provided value and return the encoded bytes directly. */\n    readonly encode: (value: TFrom) => ReadonlyUint8Array;\n    /**\n     * Writes the encoded value into the provided byte array at the given offset.\n     * Returns the offset of the next byte after the encoded value.\n     */\n    readonly write: (value: TFrom, bytes: Uint8Array, offset: Offset) => Offset;\n};\n\nexport type FixedSizeEncoder<TFrom, TSize extends number = number> = BaseEncoder<TFrom> & {\n    /** The fixed size of the encoded value in bytes. */\n    readonly fixedSize: TSize;\n};\n\nexport type VariableSizeEncoder<TFrom> = BaseEncoder<TFrom> & {\n    /** The total size of the encoded value in bytes. */\n    readonly getSizeFromValue: (value: TFrom) => number;\n    /** The maximum size an encoded value can be in bytes, if applicable. */\n    readonly maxSize?: number;\n};\n\n/**\n * An object that can encode a value to a `Uint8Array`.\n */\nexport type Encoder<TFrom> = FixedSizeEncoder<TFrom> | VariableSizeEncoder<TFrom>;\n\ntype BaseDecoder<TTo> = {\n    /** Decodes the provided byte array at the given offset (or zero) and returns the value directly. */\n    readonly decode: (bytes: ReadonlyUint8Array | Uint8Array, offset?: Offset) => TTo;\n    /**\n     * Reads the encoded value from the provided byte array at the given offset.\n     * Returns the decoded value and the offset of the next byte after the encoded value.\n     */\n    readonly read: (bytes: ReadonlyUint8Array | Uint8Array, offset: Offset) => [TTo, Offset];\n};\n\nexport type FixedSizeDecoder<TTo, TSize extends number = number> = BaseDecoder<TTo> & {\n    /** The fixed size of the encoded value in bytes. */\n    readonly fixedSize: TSize;\n};\n\nexport type VariableSizeDecoder<TTo> = BaseDecoder<TTo> & {\n    /** The maximum size an encoded value can be in bytes, if applicable. */\n    readonly maxSize?: number;\n};\n\n/**\n * An object that can decode a value from a `Uint8Array`.\n */\nexport type Decoder<TTo> = FixedSizeDecoder<TTo> | VariableSizeDecoder<TTo>;\n\nexport type FixedSizeCodec<TFrom, TTo extends TFrom = TFrom, TSize extends number = number> = FixedSizeDecoder<\n    TTo,\n    TSize\n> &\n    FixedSizeEncoder<TFrom, TSize>;\n\nexport type VariableSizeCodec<TFrom, TTo extends TFrom = TFrom> = VariableSizeDecoder<TTo> & VariableSizeEncoder<TFrom>;\n\n/**\n * An object that can encode and decode a value to and from a `Uint8Array`.\n * It supports encoding looser types than it decodes for convenience.\n * For example, a `bigint` encoder will always decode to a `bigint`\n * but can be used to encode a `number`.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value. Defaults to `TFrom`.\n */\nexport type Codec<TFrom, TTo extends TFrom = TFrom> = FixedSizeCodec<TFrom, TTo> | VariableSizeCodec<TFrom, TTo>;\n\n/**\n * Get the encoded size of a given value in bytes.\n */\nexport function getEncodedSize<TFrom>(\n    value: TFrom,\n    encoder: { fixedSize: number } | { getSizeFromValue: (value: TFrom) => number },\n): number {\n    return 'fixedSize' in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);\n}\n\n/** Fills the missing `encode` function using the existing `write` function. */\nexport function createEncoder<TFrom, TSize extends number>(\n    encoder: Omit<FixedSizeEncoder<TFrom, TSize>, 'encode'>,\n): FixedSizeEncoder<TFrom, TSize>;\nexport function createEncoder<TFrom>(encoder: Omit<VariableSizeEncoder<TFrom>, 'encode'>): VariableSizeEncoder<TFrom>;\nexport function createEncoder<TFrom>(\n    encoder: Omit<FixedSizeEncoder<TFrom>, 'encode'> | Omit<VariableSizeEncoder<TFrom>, 'encode'>,\n): Encoder<TFrom>;\nexport function createEncoder<TFrom>(\n    encoder: Omit<FixedSizeEncoder<TFrom>, 'encode'> | Omit<VariableSizeEncoder<TFrom>, 'encode'>,\n): Encoder<TFrom> {\n    return Object.freeze({\n        ...encoder,\n        encode: value => {\n            const bytes = new Uint8Array(getEncodedSize(value, encoder));\n            encoder.write(value, bytes, 0);\n            return bytes;\n        },\n    });\n}\n\n/** Fills the missing `decode` function using the existing `read` function. */\nexport function createDecoder<TTo, TSize extends number>(\n    decoder: Omit<FixedSizeDecoder<TTo, TSize>, 'decode'>,\n): FixedSizeDecoder<TTo, TSize>;\nexport function createDecoder<TTo>(decoder: Omit<VariableSizeDecoder<TTo>, 'decode'>): VariableSizeDecoder<TTo>;\nexport function createDecoder<TTo>(\n    decoder: Omit<FixedSizeDecoder<TTo>, 'decode'> | Omit<VariableSizeDecoder<TTo>, 'decode'>,\n): Decoder<TTo>;\nexport function createDecoder<TTo>(\n    decoder: Omit<FixedSizeDecoder<TTo>, 'decode'> | Omit<VariableSizeDecoder<TTo>, 'decode'>,\n): Decoder<TTo> {\n    return Object.freeze({\n        ...decoder,\n        decode: (bytes, offset = 0) => decoder.read(bytes, offset)[0],\n    });\n}\n\n/** Fills the missing `encode` and `decode` function using the existing `write` and `read` functions. */\nexport function createCodec<TFrom, TTo extends TFrom = TFrom, TSize extends number = number>(\n    codec: Omit<FixedSizeCodec<TFrom, TTo, TSize>, 'decode' | 'encode'>,\n): FixedSizeCodec<TFrom, TTo, TSize>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec: Omit<VariableSizeCodec<TFrom, TTo>, 'decode' | 'encode'>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec:\n        | Omit<FixedSizeCodec<TFrom, TTo>, 'decode' | 'encode'>\n        | Omit<VariableSizeCodec<TFrom, TTo>, 'decode' | 'encode'>,\n): Codec<TFrom, TTo>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec:\n        | Omit<FixedSizeCodec<TFrom, TTo>, 'decode' | 'encode'>\n        | Omit<VariableSizeCodec<TFrom, TTo>, 'decode' | 'encode'>,\n): Codec<TFrom, TTo> {\n    return Object.freeze({\n        ...codec,\n        decode: (bytes, offset = 0) => codec.read(bytes, offset)[0],\n        encode: value => {\n            const bytes = new Uint8Array(getEncodedSize(value, codec));\n            codec.write(value, bytes, 0);\n            return bytes;\n        },\n    });\n}\n\nexport function isFixedSize<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize> | VariableSizeEncoder<TFrom>,\n): encoder is FixedSizeEncoder<TFrom, TSize>;\nexport function isFixedSize<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize> | VariableSizeDecoder<TTo>,\n): decoder is FixedSizeDecoder<TTo, TSize>;\nexport function isFixedSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize> | VariableSizeCodec<TFrom, TTo>,\n): codec is FixedSizeCodec<TFrom, TTo, TSize>;\nexport function isFixedSize<TSize extends number>(\n    codec: { fixedSize: TSize } | { maxSize?: number },\n): codec is { fixedSize: TSize };\nexport function isFixedSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { fixedSize: number } {\n    return 'fixedSize' in codec && typeof codec.fixedSize === 'number';\n}\n\nexport function assertIsFixedSize<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize> | VariableSizeEncoder<TFrom>,\n): asserts encoder is FixedSizeEncoder<TFrom, TSize>;\nexport function assertIsFixedSize<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize> | VariableSizeDecoder<TTo>,\n): asserts decoder is FixedSizeDecoder<TTo, TSize>;\nexport function assertIsFixedSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize> | VariableSizeCodec<TFrom, TTo>,\n): asserts codec is FixedSizeCodec<TFrom, TTo, TSize>;\nexport function assertIsFixedSize<TSize extends number>(\n    codec: { fixedSize: TSize } | { maxSize?: number },\n): asserts codec is { fixedSize: TSize };\nexport function assertIsFixedSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n): asserts codec is { fixedSize: number } {\n    if (!isFixedSize(codec)) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_FIXED_LENGTH);\n    }\n}\n\nexport function isVariableSize<TFrom>(encoder: Encoder<TFrom>): encoder is VariableSizeEncoder<TFrom>;\nexport function isVariableSize<TTo>(decoder: Decoder<TTo>): decoder is VariableSizeDecoder<TTo>;\nexport function isVariableSize<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n): codec is VariableSizeCodec<TFrom, TTo>;\nexport function isVariableSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { maxSize?: number };\nexport function isVariableSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { maxSize?: number } {\n    return !isFixedSize(codec);\n}\n\nexport function assertIsVariableSize<T>(encoder: Encoder<T>): asserts encoder is VariableSizeEncoder<T>;\nexport function assertIsVariableSize<T>(decoder: Decoder<T>): asserts decoder is VariableSizeDecoder<T>;\nexport function assertIsVariableSize<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n): asserts codec is VariableSizeCodec<TFrom, TTo>;\nexport function assertIsVariableSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n): asserts codec is { maxSize?: number };\nexport function assertIsVariableSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n): asserts codec is { maxSize?: number } {\n    if (!isVariableSize(codec)) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_VARIABLE_LENGTH);\n    }\n}\n", "import {\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH,\n    SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH,\n    SolanaError,\n} from '@solana/errors';\n\nimport {\n    Codec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\n\n/**\n * Combines an encoder and a decoder into a codec.\n * The encoder and decoder must have the same fixed size, max size and description.\n * If a description is provided, it will override the encoder and decoder descriptions.\n */\nexport function combineCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize>,\n    decoder: FixedSizeDecoder<TTo, TSize>,\n): FixedSizeCodec<TFrom, TTo, TSize>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: VariableSizeEncoder<TFrom>,\n    decoder: VariableSizeDecoder<TTo>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: Encoder<TFrom>,\n    decoder: Decoder<TTo>,\n): Codec<TFrom, TTo>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: Encoder<TFrom>,\n    decoder: Decoder<TTo>,\n): Codec<TFrom, TTo> {\n    if (isFixedSize(encoder) !== isFixedSize(decoder)) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_SIZE_COMPATIBILITY_MISMATCH);\n    }\n\n    if (isFixedSize(encoder) && isFixedSize(decoder) && encoder.fixedSize !== decoder.fixedSize) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_FIXED_SIZE_MISMATCH, {\n            decoderFixedSize: decoder.fixedSize,\n            encoderFixedSize: encoder.fixedSize,\n        });\n    }\n\n    if (!isFixedSize(encoder) && !isFixedSize(decoder) && encoder.maxSize !== decoder.maxSize) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__ENCODER_DECODER_MAX_SIZE_MISMATCH, {\n            decoderMaxSize: decoder.maxSize,\n            encoderMaxSize: encoder.maxSize,\n        });\n    }\n\n    return {\n        ...decoder,\n        ...encoder,\n        decode: decoder.decode,\n        encode: encoder.encode,\n        read: decoder.read,\n        write: encoder.write,\n    };\n}\n", "import {\n    SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL,\n    SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES,\n    SolanaError,\n} from '@solana/errors';\n\nimport { containsBytes } from './bytes';\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\nimport { combineCodec } from './combine-codec';\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Creates an encoder that writes a `Uint8Array` sentinel after the encoded value.\n * This is useful to delimit the encoded value when being read by a decoder.\n *\n * Note that, if the sentinel is found in the encoded value, an error is thrown.\n */\nexport function addEncoderSentinel<TFrom>(\n    encoder: FixedSizeEncoder<TFrom>,\n    sentinel: ReadonlyUint8Array,\n): FixedSizeEncoder<TFrom>;\nexport function addEncoderSentinel<TFrom>(\n    encoder: Encoder<TFrom>,\n    sentinel: ReadonlyUint8Array,\n): VariableSizeEncoder<TFrom>;\nexport function addEncoderSentinel<TFrom>(encoder: Encoder<TFrom>, sentinel: ReadonlyUint8Array): Encoder<TFrom> {\n    const write = ((value, bytes, offset) => {\n        // Here we exceptionally use the `encode` function instead of the `write`\n        // function to contain the content of the encoder within its own bounds\n        // and to avoid writing the sentinel as part of the encoded value.\n        const encoderBytes = encoder.encode(value);\n        if (findSentinelIndex(encoderBytes, sentinel) >= 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__ENCODED_BYTES_MUST_NOT_INCLUDE_SENTINEL, {\n                encodedBytes: encoderBytes,\n                hexEncodedBytes: hexBytes(encoderBytes),\n                hexSentinel: hexBytes(sentinel),\n                sentinel,\n            });\n        }\n        bytes.set(encoderBytes, offset);\n        offset += encoderBytes.length;\n        bytes.set(sentinel, offset);\n        offset += sentinel.length;\n        return offset;\n    }) as Encoder<TFrom>['write'];\n\n    if (isFixedSize(encoder)) {\n        return createEncoder({ ...encoder, fixedSize: encoder.fixedSize + sentinel.length, write });\n    }\n\n    return createEncoder({\n        ...encoder,\n        ...(encoder.maxSize != null ? { maxSize: encoder.maxSize + sentinel.length } : {}),\n        getSizeFromValue: value => encoder.getSizeFromValue(value) + sentinel.length,\n        write,\n    });\n}\n\n/**\n * Creates a decoder that continues reading until a `Uint8Array` sentinel is found.\n *\n * If the sentinel is not found in the byte array to decode, an error is thrown.\n */\nexport function addDecoderSentinel<TTo>(\n    decoder: FixedSizeDecoder<TTo>,\n    sentinel: ReadonlyUint8Array,\n): FixedSizeDecoder<TTo>;\nexport function addDecoderSentinel<TTo>(decoder: Decoder<TTo>, sentinel: ReadonlyUint8Array): VariableSizeDecoder<TTo>;\nexport function addDecoderSentinel<TTo>(decoder: Decoder<TTo>, sentinel: ReadonlyUint8Array): Decoder<TTo> {\n    const read = ((bytes, offset) => {\n        const candidateBytes = offset === 0 ? bytes : bytes.slice(offset);\n        const sentinelIndex = findSentinelIndex(candidateBytes, sentinel);\n        if (sentinelIndex === -1) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__SENTINEL_MISSING_IN_DECODED_BYTES, {\n                decodedBytes: candidateBytes,\n                hexDecodedBytes: hexBytes(candidateBytes),\n                hexSentinel: hexBytes(sentinel),\n                sentinel,\n            });\n        }\n        const preSentinelBytes = candidateBytes.slice(0, sentinelIndex);\n        // Here we exceptionally use the `decode` function instead of the `read`\n        // function to contain the content of the decoder within its own bounds\n        // and ensure that the sentinel is not part of the decoded value.\n        return [decoder.decode(preSentinelBytes), offset + preSentinelBytes.length + sentinel.length];\n    }) as Decoder<TTo>['read'];\n\n    if (isFixedSize(decoder)) {\n        return createDecoder({ ...decoder, fixedSize: decoder.fixedSize + sentinel.length, read });\n    }\n\n    return createDecoder({\n        ...decoder,\n        ...(decoder.maxSize != null ? { maxSize: decoder.maxSize + sentinel.length } : {}),\n        read,\n    });\n}\n\n/**\n * Creates a Codec that writes a `Uint8Array` sentinel after the encoded\n * value and, when decoding, continues reading until the sentinel is found.\n *\n * Note that, if the sentinel is found in the encoded value\n * or not found in the byte array to decode, an error is thrown.\n */\nexport function addCodecSentinel<TFrom, TTo extends TFrom>(\n    codec: FixedSizeCodec<TFrom, TTo>,\n    sentinel: ReadonlyUint8Array,\n): FixedSizeCodec<TFrom, TTo>;\nexport function addCodecSentinel<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    sentinel: ReadonlyUint8Array,\n): VariableSizeCodec<TFrom, TTo>;\nexport function addCodecSentinel<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    sentinel: ReadonlyUint8Array,\n): Codec<TFrom, TTo> {\n    return combineCodec(addEncoderSentinel(codec, sentinel), addDecoderSentinel(codec, sentinel));\n}\n\nfunction findSentinelIndex(bytes: ReadonlyUint8Array, sentinel: ReadonlyUint8Array) {\n    return bytes.findIndex((byte, index, arr) => {\n        if (sentinel.length === 1) return byte === sentinel[0];\n        return containsBytes(arr, sentinel, index);\n    });\n}\n\nfunction hexBytes(bytes: ReadonlyUint8Array): string {\n    return bytes.reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n}\n", "import {\n    SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY,\n    SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH,\n    SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE,\n    SolanaError,\n} from '@solana/errors';\n\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Asserts that a given byte array is not empty.\n */\nexport function assertByteArrayIsNotEmptyForCodec(\n    codecDescription: string,\n    bytes: ReadonlyUint8Array | Uint8Array,\n    offset = 0,\n) {\n    if (bytes.length - offset <= 0) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__CANNOT_DECODE_EMPTY_BYTE_ARRAY, {\n            codecDescription,\n        });\n    }\n}\n\n/**\n * Asserts that a given byte array has enough bytes to decode.\n */\nexport function assertByteArrayHasEnoughBytesForCodec(\n    codecDescription: string,\n    expected: number,\n    bytes: ReadonlyUint8Array | Uint8Array,\n    offset = 0,\n) {\n    const bytesLength = bytes.length - offset;\n    if (bytesLength < expected) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_BYTE_LENGTH, {\n            bytesLength,\n            codecDescription,\n            expected,\n        });\n    }\n}\n\n/**\n * Asserts that a given offset is within the byte array bounds.\n * This range is between 0 and the byte array length and is inclusive.\n * An offset equals to the byte array length is considered a valid offset\n * as it allows the post-offset of codecs to signal the end of the byte array.\n */\nexport function assertByteArrayOffsetIsNotOutOfRange(codecDescription: string, offset: number, bytesLength: number) {\n    if (offset < 0 || offset > bytesLength) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE, {\n            bytesLength,\n            codecDescription,\n            offset,\n        });\n    }\n}\n", "import { assertByteArrayHasEnoughBytesForCodec } from './assertions';\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\nimport { combineCodec } from './combine-codec';\n\ntype NumberEncoder = Encoder<bigint | number> | Encoder<number>;\ntype FixedSizeNumberEncoder<TSize extends number = number> =\n    | FixedSizeEncoder<bigint | number, TSize>\n    | FixedSizeEncoder<number, TSize>;\ntype NumberDecoder = Decoder<bigint> | Decoder<number>;\ntype FixedSizeNumberDecoder<TSize extends number = number> =\n    | FixedSizeDecoder<bigint, TSize>\n    | FixedSizeDecoder<number, TSize>;\ntype NumberCodec = Codec<bigint | number, bigint> | Codec<number>;\ntype FixedSizeNumberCodec<TSize extends number = number> =\n    | FixedSizeCodec<bigint | number, bigint, TSize>\n    | FixedSizeCodec<number, number, TSize>;\n\n/**\n * Stores the size of the `encoder` in bytes as a prefix using the `prefix` encoder.\n */\nexport function addEncoderSizePrefix<TFrom>(\n    encoder: FixedSizeEncoder<TFrom>,\n    prefix: FixedSizeNumberEncoder,\n): FixedSizeEncoder<TFrom>;\nexport function addEncoderSizePrefix<TFrom>(encoder: Encoder<TFrom>, prefix: NumberEncoder): VariableSizeEncoder<TFrom>;\nexport function addEncoderSizePrefix<TFrom>(encoder: Encoder<TFrom>, prefix: NumberEncoder): Encoder<TFrom> {\n    const write = ((value, bytes, offset) => {\n        // Here we exceptionally use the `encode` function instead of the `write`\n        // function to contain the content of the encoder within its own bounds.\n        const encoderBytes = encoder.encode(value);\n        offset = prefix.write(encoderBytes.length, bytes, offset);\n        bytes.set(encoderBytes, offset);\n        return offset + encoderBytes.length;\n    }) as Encoder<TFrom>['write'];\n\n    if (isFixedSize(prefix) && isFixedSize(encoder)) {\n        return createEncoder({ ...encoder, fixedSize: prefix.fixedSize + encoder.fixedSize, write });\n    }\n\n    const prefixMaxSize = isFixedSize(prefix) ? prefix.fixedSize : (prefix.maxSize ?? null);\n    const encoderMaxSize = isFixedSize(encoder) ? encoder.fixedSize : (encoder.maxSize ?? null);\n    const maxSize = prefixMaxSize !== null && encoderMaxSize !== null ? prefixMaxSize + encoderMaxSize : null;\n\n    return createEncoder({\n        ...encoder,\n        ...(maxSize !== null ? { maxSize } : {}),\n        getSizeFromValue: value => {\n            const encoderSize = getEncodedSize(value, encoder);\n            return getEncodedSize(encoderSize, prefix) + encoderSize;\n        },\n        write,\n    });\n}\n\n/**\n * Bounds the size of the `decoder` by reading the `prefix` encoder prefix.\n */\nexport function addDecoderSizePrefix<TTo>(\n    decoder: FixedSizeDecoder<TTo>,\n    prefix: FixedSizeNumberDecoder,\n): FixedSizeDecoder<TTo>;\nexport function addDecoderSizePrefix<TTo>(decoder: Decoder<TTo>, prefix: NumberDecoder): VariableSizeDecoder<TTo>;\nexport function addDecoderSizePrefix<TTo>(decoder: Decoder<TTo>, prefix: NumberDecoder): Decoder<TTo> {\n    const read = ((bytes, offset) => {\n        const [bigintSize, decoderOffset] = prefix.read(bytes, offset);\n        const size = Number(bigintSize);\n        offset = decoderOffset;\n        // Slice the byte array to the contained size if necessary.\n        if (offset > 0 || bytes.length > size) {\n            bytes = bytes.slice(offset, offset + size);\n        }\n        assertByteArrayHasEnoughBytesForCodec('addDecoderSizePrefix', size, bytes);\n        // Here we exceptionally use the `decode` function instead of the `read`\n        // function to contain the content of the decoder within its own bounds.\n        return [decoder.decode(bytes), offset + size];\n    }) as Decoder<TTo>['read'];\n\n    if (isFixedSize(prefix) && isFixedSize(decoder)) {\n        return createDecoder({ ...decoder, fixedSize: prefix.fixedSize + decoder.fixedSize, read });\n    }\n\n    const prefixMaxSize = isFixedSize(prefix) ? prefix.fixedSize : (prefix.maxSize ?? null);\n    const decoderMaxSize = isFixedSize(decoder) ? decoder.fixedSize : (decoder.maxSize ?? null);\n    const maxSize = prefixMaxSize !== null && decoderMaxSize !== null ? prefixMaxSize + decoderMaxSize : null;\n    return createDecoder({ ...decoder, ...(maxSize !== null ? { maxSize } : {}), read });\n}\n\n/**\n * Bounds the size of the `codec` using the provided `prefix` codec prefix.\n */\nexport function addCodecSizePrefix<TFrom, TTo extends TFrom>(\n    codec: FixedSizeCodec<TFrom, TTo>,\n    prefix: FixedSizeNumberCodec,\n): FixedSizeCodec<TFrom, TTo>;\nexport function addCodecSizePrefix<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    prefix: NumberCodec,\n): VariableSizeCodec<TFrom, TTo>;\nexport function addCodecSizePrefix<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    prefix: NumberCodec,\n): Codec<TFrom, TTo> {\n    return combineCodec(addEncoderSizePrefix(codec, prefix), addDecoderSizePrefix(codec, prefix));\n}\n", "import { assertByteArrayHasEnoughBytesForCodec } from './assertions';\nimport { fixBytes } from './bytes';\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    Offset,\n} from './codec';\nimport { combineCodec } from './combine-codec';\n\n/**\n * Creates a fixed-size encoder from a given encoder.\n *\n * @param encoder - The encoder to wrap into a fixed-size encoder.\n * @param fixedBytes - The fixed number of bytes to write.\n */\nexport function fixEncoderSize<TFrom, TSize extends number>(\n    encoder: Encoder<TFrom>,\n    fixedBytes: TSize,\n): FixedSizeEncoder<TFrom, TSize> {\n    return createEncoder({\n        fixedSize: fixedBytes,\n        write: (value: TFrom, bytes: Uint8Array, offset: Offset) => {\n            // Here we exceptionally use the `encode` function instead of the `write`\n            // function as using the nested `write` function on a fixed-sized byte\n            // array may result in a out-of-bounds error on the nested encoder.\n            const variableByteArray = encoder.encode(value);\n            const fixedByteArray =\n                variableByteArray.length > fixedBytes ? variableByteArray.slice(0, fixedBytes) : variableByteArray;\n            bytes.set(fixedByteArray, offset);\n            return offset + fixedBytes;\n        },\n    });\n}\n\n/**\n * Creates a fixed-size decoder from a given decoder.\n *\n * @param decoder - The decoder to wrap into a fixed-size decoder.\n * @param fixedBytes - The fixed number of bytes to read.\n */\nexport function fixDecoderSize<TTo, TSize extends number>(\n    decoder: Decoder<TTo>,\n    fixedBytes: TSize,\n): FixedSizeDecoder<TTo, TSize> {\n    return createDecoder({\n        fixedSize: fixedBytes,\n        read: (bytes, offset) => {\n            assertByteArrayHasEnoughBytesForCodec('fixCodecSize', fixedBytes, bytes, offset);\n            // Slice the byte array to the fixed size if necessary.\n            if (offset > 0 || bytes.length > fixedBytes) {\n                bytes = bytes.slice(offset, offset + fixedBytes);\n            }\n            // If the nested decoder is fixed-size, pad and truncate the byte array accordingly.\n            if (isFixedSize(decoder)) {\n                bytes = fixBytes(bytes, decoder.fixedSize);\n            }\n            // Decode the value using the nested decoder.\n            const [value] = decoder.read(bytes, 0);\n            return [value, offset + fixedBytes];\n        },\n    });\n}\n\n/**\n * Creates a fixed-size codec from a given codec.\n *\n * @param codec - The codec to wrap into a fixed-size codec.\n * @param fixedBytes - The fixed number of bytes to read/write.\n */\nexport function fixCodecSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: Codec<TFrom, TTo>,\n    fixedBytes: TSize,\n): FixedSizeCodec<TFrom, TTo, TSize> {\n    return combineCodec(fixEncoderSize(codec, fixedBytes), fixDecoderSize(codec, fixedBytes));\n}\n", "import { assertByteArrayOffsetIsNotOutOfRange } from './assertions';\nimport { Codec, createDecoder, createEncoder, Decoder, Encoder, Offset } from './codec';\nimport { combineCodec } from './combine-codec';\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyEncoder = Encoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyDecoder = Decoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyCodec = Codec<any>;\n\ntype OffsetConfig = {\n    postOffset?: PostOffsetFunction;\n    preOffset?: PreOffsetFunction;\n};\n\ntype PreOffsetFunctionScope = {\n    /** The entire byte array. */\n    bytes: ReadonlyUint8Array | Uint8Array;\n    /** The original offset prior to encode or decode. */\n    preOffset: Offset;\n    /** Wraps the offset to the byte array length. */\n    wrapBytes: (offset: Offset) => Offset;\n};\n\ntype PreOffsetFunction = (scope: PreOffsetFunctionScope) => Offset;\ntype PostOffsetFunction = (\n    scope: PreOffsetFunctionScope & {\n        /** The modified offset used to encode or decode. */\n        newPreOffset: Offset;\n        /** The original offset returned by the encoder or decoder. */\n        postOffset: Offset;\n    },\n) => Offset;\n\n/**\n * Moves the offset of a given encoder.\n */\nexport function offsetEncoder<TEncoder extends AnyEncoder>(encoder: TEncoder, config: OffsetConfig): TEncoder {\n    return createEncoder({\n        ...encoder,\n        write: (value, bytes, preOffset) => {\n            const wrapBytes = (offset: Offset) => modulo(offset, bytes.length);\n            const newPreOffset = config.preOffset ? config.preOffset({ bytes, preOffset, wrapBytes }) : preOffset;\n            assertByteArrayOffsetIsNotOutOfRange('offsetEncoder', newPreOffset, bytes.length);\n            const postOffset = encoder.write(value, bytes, newPreOffset);\n            const newPostOffset = config.postOffset\n                ? config.postOffset({ bytes, newPreOffset, postOffset, preOffset, wrapBytes })\n                : postOffset;\n            assertByteArrayOffsetIsNotOutOfRange('offsetEncoder', newPostOffset, bytes.length);\n            return newPostOffset;\n        },\n    }) as TEncoder;\n}\n\n/**\n * Moves the offset of a given decoder.\n */\nexport function offsetDecoder<TDecoder extends AnyDecoder>(decoder: TDecoder, config: OffsetConfig): TDecoder {\n    return createDecoder({\n        ...decoder,\n        read: (bytes, preOffset) => {\n            const wrapBytes = (offset: Offset) => modulo(offset, bytes.length);\n            const newPreOffset = config.preOffset ? config.preOffset({ bytes, preOffset, wrapBytes }) : preOffset;\n            assertByteArrayOffsetIsNotOutOfRange('offsetDecoder', newPreOffset, bytes.length);\n            const [value, postOffset] = decoder.read(bytes, newPreOffset);\n            const newPostOffset = config.postOffset\n                ? config.postOffset({ bytes, newPreOffset, postOffset, preOffset, wrapBytes })\n                : postOffset;\n            assertByteArrayOffsetIsNotOutOfRange('offsetDecoder', newPostOffset, bytes.length);\n            return [value, newPostOffset];\n        },\n    }) as TDecoder;\n}\n\n/**\n * Moves the offset of a given codec.\n */\nexport function offsetCodec<TCodec extends AnyCodec>(codec: TCodec, config: OffsetConfig): TCodec {\n    return combineCodec(offsetEncoder(codec, config), offsetDecoder(codec, config)) as TCodec;\n}\n\n/** A modulo function that handles negative dividends and zero divisors. */\nfunction modulo(dividend: number, divisor: number) {\n    if (divisor === 0) return 0;\n    return ((dividend % divisor) + divisor) % divisor;\n}\n", "import { SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH, SolanaError } from '@solana/errors';\n\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n} from './codec';\nimport { combineCodec } from './combine-codec';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyEncoder = Encoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyDecoder = Decoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyCodec = Codec<any>;\n\n/**\n * Updates the size of a given encoder.\n */\nexport function resizeEncoder<TFrom, TSize extends number, TNewSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize>,\n    resize: (size: TSize) => TNewSize,\n): FixedSizeEncoder<TFrom, TNewSize>;\nexport function resizeEncoder<TEncoder extends AnyEncoder>(\n    encoder: TEncoder,\n    resize: (size: number) => number,\n): TEncoder;\nexport function resizeEncoder<TEncoder extends AnyEncoder>(\n    encoder: TEncoder,\n    resize: (size: number) => number,\n): TEncoder {\n    if (isFixedSize(encoder)) {\n        const fixedSize = resize(encoder.fixedSize);\n        if (fixedSize < 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH, {\n                bytesLength: fixedSize,\n                codecDescription: 'resizeEncoder',\n            });\n        }\n        return createEncoder({ ...encoder, fixedSize }) as TEncoder;\n    }\n    return createEncoder({\n        ...encoder,\n        getSizeFromValue: value => {\n            const newSize = resize(encoder.getSizeFromValue(value));\n            if (newSize < 0) {\n                throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH, {\n                    bytesLength: newSize,\n                    codecDescription: 'resizeEncoder',\n                });\n            }\n            return newSize;\n        },\n    }) as TEncoder;\n}\n\n/**\n * Updates the size of a given decoder.\n */\n\nexport function resizeDecoder<TFrom, TSize extends number, TNewSize extends number>(\n    decoder: FixedSizeDecoder<TFrom, TSize>,\n    resize: (size: TSize) => TNewSize,\n): FixedSizeDecoder<TFrom, TNewSize>;\nexport function resizeDecoder<TDecoder extends AnyDecoder>(\n    decoder: TDecoder,\n    resize: (size: number) => number,\n): TDecoder;\nexport function resizeDecoder<TDecoder extends AnyDecoder>(\n    decoder: TDecoder,\n    resize: (size: number) => number,\n): TDecoder {\n    if (isFixedSize(decoder)) {\n        const fixedSize = resize(decoder.fixedSize);\n        if (fixedSize < 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__EXPECTED_POSITIVE_BYTE_LENGTH, {\n                bytesLength: fixedSize,\n                codecDescription: 'resizeDecoder',\n            });\n        }\n        return createDecoder({ ...decoder, fixedSize }) as TDecoder;\n    }\n    return decoder;\n}\n\n/**\n * Updates the size of a given codec.\n */\nexport function resizeCodec<TFrom, TTo extends TFrom, TSize extends number, TNewSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize>,\n    resize: (size: TSize) => TNewSize,\n): FixedSizeCodec<TFrom, TTo, TNewSize>;\nexport function resizeCodec<TCodec extends AnyCodec>(codec: TCodec, resize: (size: number) => number): TCodec;\nexport function resizeCodec<TCodec extends AnyCodec>(codec: TCodec, resize: (size: number) => number): TCodec {\n    return combineCodec(resizeEncoder(codec, resize), resizeDecoder(codec, resize)) as TCodec;\n}\n", "import { Codec, Decoder, Encoder, Offset } from './codec';\nimport { combineCodec } from './combine-codec';\nimport { offsetDecoder, offsetEncoder } from './offset-codec';\nimport { resizeDecoder, resizeEncoder } from './resize-codec';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyEncoder = Encoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyDecoder = Decoder<any>;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyCodec = Codec<any>;\n\n/**\n * Adds left padding to the given encoder.\n */\nexport function padLeftEncoder<TEncoder extends AnyEncoder>(encoder: TEncoder, offset: Offset): TEncoder {\n    return offsetEncoder(\n        resizeEncoder(encoder, size => size + offset),\n        { preOffset: ({ preOffset }) => preOffset + offset },\n    );\n}\n\n/**\n * Adds right padding to the given encoder.\n */\nexport function padRightEncoder<TEncoder extends AnyEncoder>(encoder: TEncoder, offset: Offset): TEncoder {\n    return offsetEncoder(\n        resizeEncoder(encoder, size => size + offset),\n        { postOffset: ({ postOffset }) => postOffset + offset },\n    );\n}\n\n/**\n * Adds left padding to the given decoder.\n */\nexport function padLeftDecoder<TDecoder extends AnyDecoder>(decoder: TDecoder, offset: Offset): TDecoder {\n    return offsetDecoder(\n        resizeDecoder(decoder, size => size + offset),\n        { preOffset: ({ preOffset }) => preOffset + offset },\n    );\n}\n\n/**\n * Adds right padding to the given decoder.\n */\nexport function padRightDecoder<TDecoder extends AnyDecoder>(decoder: TDecoder, offset: Offset): TDecoder {\n    return offsetDecoder(\n        resizeDecoder(decoder, size => size + offset),\n        { postOffset: ({ postOffset }) => postOffset + offset },\n    );\n}\n\n/**\n * Adds left padding to the given codec.\n */\nexport function padLeftCodec<TCodec extends AnyCodec>(codec: TCodec, offset: Offset): TCodec {\n    return combineCodec(padLeftEncoder(codec, offset), padLeftDecoder(codec, offset)) as TCodec;\n}\n\n/**\n * Adds right padding to the given codec.\n */\nexport function padRightCodec<TCodec extends AnyCodec>(codec: TCodec, offset: Offset): TCodec {\n    return combineCodec(padRightEncoder(codec, offset), padRightDecoder(codec, offset)) as TCodec;\n}\n", "import {\n    assertIsFixedSize,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n} from './codec';\nimport { combineCodec } from './combine-codec';\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\nfunction copySourceToTargetInReverse(\n    source: ReadonlyUint8Array,\n    target_WILL_MUTATE: Uint8Array,\n    sourceOffset: number,\n    sourceLength: number,\n    targetOffset: number = 0,\n) {\n    while (sourceOffset < --sourceLength) {\n        const leftValue = source[sourceOffset];\n        target_WILL_MUTATE[sourceOffset + targetOffset] = source[sourceLength];\n        target_WILL_MUTATE[sourceLength + targetOffset] = leftValue;\n        sourceOffset++;\n    }\n    if (sourceOffset === sourceLength) {\n        target_WILL_MUTATE[sourceOffset + targetOffset] = source[sourceOffset];\n    }\n}\n\n/**\n * Reverses the bytes of a fixed-size encoder.\n */\nexport function reverseEncoder<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize>,\n): FixedSizeEncoder<TFrom, TSize> {\n    assertIsFixedSize(encoder);\n    return createEncoder({\n        ...encoder,\n        write: (value: TFrom, bytes, offset) => {\n            const newOffset = encoder.write(value, bytes, offset);\n            copySourceToTargetInReverse(\n                bytes /* source */,\n                bytes /* target_WILL_MUTATE */,\n                offset /* sourceOffset */,\n                offset + encoder.fixedSize /* sourceLength */,\n            );\n            return newOffset;\n        },\n    });\n}\n\n/**\n * Reverses the bytes of a fixed-size decoder.\n */\nexport function reverseDecoder<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize>,\n): FixedSizeDecoder<TTo, TSize> {\n    assertIsFixedSize(decoder);\n    return createDecoder({\n        ...decoder,\n        read: (bytes, offset) => {\n            const reversedBytes = bytes.slice();\n            copySourceToTargetInReverse(\n                bytes /* source */,\n                reversedBytes /* target_WILL_MUTATE */,\n                offset /* sourceOffset */,\n                offset + decoder.fixedSize /* sourceLength */,\n            );\n            return decoder.read(reversedBytes, offset);\n        },\n    });\n}\n\n/**\n * Reverses the bytes of a fixed-size codec.\n */\nexport function reverseCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize>,\n): FixedSizeCodec<TFrom, TTo, TSize> {\n    return combineCodec(reverseEncoder(codec), reverseDecoder(codec));\n}\n", "import {\n    Codec,\n    createCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isVariableSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\nimport { ReadonlyUint8Array } from './readonly-uint8array';\n\n/**\n * Converts an encoder A to a encoder B by mapping their values.\n */\nexport function transformEncoder<TOldFrom, TNewFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TOldFrom, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): FixedSizeEncoder<TNewFrom, TSize>;\nexport function transformEncoder<TOldFrom, TNewFrom>(\n    encoder: VariableSizeEncoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): VariableSizeEncoder<TNewFrom>;\nexport function transformEncoder<TOldFrom, TNewFrom>(\n    encoder: Encoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Encoder<TNewFrom>;\nexport function transformEncoder<TOldFrom, TNewFrom>(\n    encoder: Encoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Encoder<TNewFrom> {\n    return createEncoder({\n        ...(isVariableSize(encoder)\n            ? { ...encoder, getSizeFromValue: (value: TNewFrom) => encoder.getSizeFromValue(unmap(value)) }\n            : encoder),\n        write: (value: TNewFrom, bytes, offset) => encoder.write(unmap(value), bytes, offset),\n    });\n}\n\n/**\n * Converts an decoder A to a decoder B by mapping their values.\n */\nexport function transformDecoder<TOldTo, TNewTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TOldTo, TSize>,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): FixedSizeDecoder<TNewTo, TSize>;\nexport function transformDecoder<TOldTo, TNewTo>(\n    decoder: VariableSizeDecoder<TOldTo>,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): VariableSizeDecoder<TNewTo>;\nexport function transformDecoder<TOldTo, TNewTo>(\n    decoder: Decoder<TOldTo>,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): Decoder<TNewTo>;\nexport function transformDecoder<TOldTo, TNewTo>(\n    decoder: Decoder<TOldTo>,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): Decoder<TNewTo> {\n    return createDecoder({\n        ...decoder,\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            const [value, newOffset] = decoder.read(bytes, offset);\n            return [map(value, bytes, offset), newOffset];\n        },\n    });\n}\n\n/**\n * Converts a codec A to a codec B by mapping their values.\n */\nexport function transformCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom, TSize extends number>(\n    codec: FixedSizeCodec<TOldFrom, TTo, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): FixedSizeCodec<TNewFrom, TTo, TSize>;\nexport function transformCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom>(\n    codec: VariableSizeCodec<TOldFrom, TTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): VariableSizeCodec<TNewFrom, TTo>;\nexport function transformCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom>(\n    codec: Codec<TOldFrom, TTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Codec<TNewFrom, TTo>;\nexport function transformCodec<\n    TOldFrom,\n    TNewFrom,\n    TOldTo extends TOldFrom,\n    TNewTo extends TNewFrom,\n    TSize extends number,\n>(\n    codec: FixedSizeCodec<TOldFrom, TOldTo, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): FixedSizeCodec<TNewFrom, TNewTo, TSize>;\nexport function transformCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: VariableSizeCodec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): VariableSizeCodec<TNewFrom, TNewTo>;\nexport function transformCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: Codec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): Codec<TNewFrom, TNewTo>;\nexport function transformCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: Codec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map?: (value: TOldTo, bytes: ReadonlyUint8Array | Uint8Array, offset: number) => TNewTo,\n): Codec<TNewFrom, TNewTo> {\n    return createCodec({\n        ...transformEncoder(codec, unmap),\n        read: map ? transformDecoder(codec, map).read : (codec.read as unknown as Decoder<TNewTo>['read']),\n    });\n}\n"]}